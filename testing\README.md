# ATMA E2E Load Testing Suite

Comprehensive End-to-End Load Testing Suite untuk ATMA (AI-Driven Talent Mapping Assessment) Backend Services.

## 🎯 Overview

Testing suite ini mensimulasikan skenario real-world dengan 50 user yang melakukan complete user journey:

1. **📝 Registration** - 50 users register dengan data random
2. **🔐 Login** - 50 users login bersamaan
3. **👤 Profile Update** - 50 users update profile mereka
4. **🎯 Assessment Submission** - 50 users submit assessment
5. **🔔 WebSocket Notifications** - 50 users menunggu completion via WebSocket
6. **📊 Results Check** - 50 users cek hasil assessment
7. **🗑️ Account Deletion** - 50 users hapus akun mereka

## 🚀 Quick Start

### Prerequisites

- Node.js >= 16.0.0
- ATMA Backend Services running:
  - API Gateway (port 3000)
  - Auth Service (port 3001)
  - Assessment Service (port 3003)
  - Archive Service (port 3002)
  - Notification Service (port 3005)

### Installation

```bash
cd testing
npm install
```

### Running Tests

```bash
# Default test (50 users)
npm test

# Small load test (10 users)
npm run test:small

# Medium load test (25 users)
npm run test:medium

# Large load test (50 users)
npm run test:large

# Stress test (100 users)
npm run test:stress
```

## 📊 Test Reports

Setiap test menghasilkan laporan komprehensif yang mencakup:

### Per-Stage Metrics
- **Success Rate** - Persentase keberhasilan per stage
- **Response Times** - Min, Max, Average, Median, 95th, 99th percentile
- **Throughput** - Requests per second

### Overall Summary
- Total requests, successes, failures
- Overall success rate
- Total test duration
- Overall throughput

### Sample Output
```
📈 LOAD TEST REPORT
================================================================================

Test Configuration:
• Users: 50
• Base URL: http://localhost:3000
• WebSocket URL: http://localhost:3005
• Concurrent Limit: 10
• Timeout: 30000ms

📝 Registration:
  • Success Rate: 98.0% (49/50)
  • Response Times (ms):
    - Min: 145ms
    - Max: 2341ms
    - Avg: 567ms
    - Median: 523ms
    - 95th percentile: 1234ms
    - 99th percentile: 2100ms
  • Throughput: 8.65 requests/second

🔐 Login:
  • Success Rate: 100.0% (50/50)
  • Response Times (ms):
    - Min: 89ms
    - Max: 1567ms
    - Avg: 234ms
    - Median: 198ms
    - 95th percentile: 567ms
    - 99th percentile: 1234ms
  • Throughput: 12.34 requests/second

... (other stages)

================================================================================
📊 OVERALL SUMMARY:
• Total Requests: 350
• Total Successes: 342
• Total Failures: 8
• Overall Success Rate: 97.7%
• Total Test Duration: 45.67 seconds
• Overall Throughput: 7.49 requests/second
================================================================================
```

## 🔧 Configuration

### Environment Variables

```bash
# API Gateway URL
BASE_URL=http://localhost:3000

# WebSocket URL
WEBSOCKET_URL=http://localhost:3005

# Number of test users
USER_COUNT=50

# Request timeout (ms)
TIMEOUT=30000

# Concurrent request limit
CONCURRENT_LIMIT=10
```

### Command Line Arguments

```bash
# Custom user count
node e2e-load-test.js --users=25

# Custom base URL
node e2e-load-test.js --base-url=http://localhost:3000

# Custom WebSocket URL
node e2e-load-test.js --websocket-url=http://localhost:3005
```

## 🧪 Test Data Generation

### User Data
- **Email**: Unique faker-generated emails
- **Password**: Random 12-character passwords (letters + numbers + 1 uppercase)
- **Username**: Unique usernames based on names
- **Full Name**: Realistic first + last names
- **Date of Birth**: Random dates (18-65 years old)
- **Gender**: Random selection from valid options

### Assessment Data
- **RIASEC**: 6 dimensions with random scores (0-100)
- **OCEAN**: 5 personality traits with random scores (0-100)
- **VIA-IS**: All 24 character strengths with random scores (0-100)

## 🔍 Monitoring & Debugging

### Real-time Progress
- ✓ Green checkmarks for successful operations
- ✗ Red X marks for failed operations
- T Yellow T for timeout operations

### Error Handling
- Detailed error messages for failed operations
- Graceful handling of network timeouts
- Proper cleanup of WebSocket connections

### Debug Mode
```bash
# Enable debug logging
DEBUG=* node e2e-load-test.js
```

## 🏗️ Architecture

### Test Flow
```
User Generation → Registration → Login → Profile Update → 
Assessment Submission → WebSocket Notifications → 
Results Check → Account Deletion → Report Generation
```

### Concurrency Control
- Batch processing dengan configurable concurrent limit
- Rate limiting untuk prevent server overload
- Proper connection pooling untuk WebSocket

### Error Recovery
- Retry mechanism untuk failed requests
- Graceful degradation untuk partial failures
- Comprehensive error reporting

## 📋 Test Scenarios

### Load Testing Scenarios

1. **Light Load** (10 users)
   - Baseline performance testing
   - Feature validation

2. **Medium Load** (25 users)
   - Normal usage simulation
   - Performance benchmarking

3. **Heavy Load** (50 users)
   - Peak usage simulation
   - Stress testing

4. **Stress Test** (100 users)
   - System limits testing
   - Breaking point identification

### WebSocket Testing
- Concurrent connections handling
- Real-time notification delivery
- Connection stability under load
- Authentication timeout handling

## 🚨 Troubleshooting

### Common Issues

1. **Connection Refused**
   ```
   Error: connect ECONNREFUSED 127.0.0.1:3000
   ```
   **Solution**: Ensure all ATMA services are running

2. **Authentication Failures**
   ```
   Error: 401 Unauthorized
   ```
   **Solution**: Check JWT token generation and validation

3. **WebSocket Timeouts**
   ```
   Error: Timeout waiting for notification
   ```
   **Solution**: Increase timeout or check notification service

4. **Rate Limiting**
   ```
   Error: 429 Too Many Requests
   ```
   **Solution**: Reduce concurrent limit or increase rate limits

### Debug Steps

1. Check service health endpoints
2. Verify database connections
3. Monitor server logs
4. Check network connectivity
5. Validate test data generation

## 📈 Performance Benchmarks

### Expected Performance (50 users)
- **Registration**: ~500ms average response time
- **Login**: ~200ms average response time
- **Profile Update**: ~300ms average response time
- **Assessment Submission**: ~800ms average response time
- **WebSocket Notifications**: ~2-5 minutes (processing time)
- **Results Check**: ~400ms average response time
- **Account Deletion**: ~300ms average response time

### Success Rate Targets
- **Overall Success Rate**: > 95%
- **Individual Stage Success**: > 98%
- **WebSocket Delivery**: > 90%

## 🔒 Security Considerations

- Unique JWT tokens per user
- Proper authentication headers
- Secure WebSocket connections
- Data cleanup after tests
- No sensitive data in logs

## 📝 Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new features
4. Ensure all tests pass
5. Submit pull request

## 📞 Support

For issues or questions:
- Check troubleshooting section
- Review server logs
- Contact development team

---

**Last Updated**: 2024-01-21  
**Version**: 1.0.0  
**Node.js**: >= 16.0.0
