{"name": "atma-backend-testing", "version": "1.0.0", "description": "AI-Driven Talent Mapping Assessment Backend Testing Suite", "main": "e2e-load-test.js", "scripts": {"test": "node e2e-load-test.js", "test:small": "node e2e-load-test.js --users=10", "test:medium": "node e2e-load-test.js --users=25", "test:large": "node e2e-load-test.js --users=50", "test:stress": "node e2e-load-test.js --users=100", "install-deps": "npm install"}, "keywords": ["testing", "e2e", "load-testing", "api-testing", "atma", "backend", "websocket"], "author": "ATMA Development Team", "license": "ISC", "dependencies": {"axios": "^1.10.0", "socket.io-client": "^4.7.5", "uuid": "^11.1.0", "@faker-js/faker": "^8.3.1", "chalk": "^4.1.2"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}