#!/bin/bash

# ATMA E2E Load Testing Runner Script
# This script provides easy commands to run different types of load tests

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default configuration
DEFAULT_BASE_URL="http://localhost:3000"
DEFAULT_WEBSOCKET_URL="http://localhost:3005"
DEFAULT_USERS=50
DEFAULT_TIMEOUT=30000
DEFAULT_CONCURRENT=10

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if services are running
check_services() {
    print_info "Checking ATMA services..."
    
    # Check API Gateway
    if curl -s "$DEFAULT_BASE_URL/health" > /dev/null 2>&1; then
        print_success "API Gateway is running (port 3000)"
    else
        print_error "API Gateway is not running on port 3000"
        return 1
    fi
    
    # Check Notification Service
    if curl -s "$DEFAULT_WEBSOCKET_URL/health" > /dev/null 2>&1; then
        print_success "Notification Service is running (port 3005)"
    else
        print_error "Notification Service is not running on port 3005"
        return 1
    fi
    
    print_success "All required services are running!"
}

# Function to install dependencies
install_deps() {
    print_info "Installing dependencies..."
    
    if [ ! -f "package.json" ]; then
        print_error "package.json not found. Please run this script from the testing directory."
        exit 1
    fi
    
    npm install
    print_success "Dependencies installed successfully!"
}

# Function to run load test
run_test() {
    local users=${1:-$DEFAULT_USERS}
    local base_url=${2:-$DEFAULT_BASE_URL}
    local websocket_url=${3:-$DEFAULT_WEBSOCKET_URL}
    local timeout=${4:-$DEFAULT_TIMEOUT}
    local concurrent=${5:-$DEFAULT_CONCURRENT}
    
    print_info "Starting load test with $users users..."
    print_info "Base URL: $base_url"
    print_info "WebSocket URL: $websocket_url"
    print_info "Timeout: ${timeout}ms"
    print_info "Concurrent Limit: $concurrent"
    
    node e2e-load-test.js \
        --users=$users \
        --base-url=$base_url \
        --websocket-url=$websocket_url \
        --timeout=$timeout \
        --concurrent=$concurrent
}

# Function to show help
show_help() {
    echo "ATMA E2E Load Testing Runner"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  install         Install dependencies"
    echo "  check           Check if services are running"
    echo "  test            Run default load test (50 users)"
    echo "  test-small      Run small load test (10 users)"
    echo "  test-medium     Run medium load test (25 users)"
    echo "  test-large      Run large load test (50 users)"
    echo "  test-stress     Run stress test (100 users)"
    echo "  custom          Run custom test with specified parameters"
    echo "  help            Show this help message"
    echo ""
    echo "Options for custom test:"
    echo "  --users=N       Number of users (default: 50)"
    echo "  --base-url=URL  API Gateway URL (default: http://localhost:3000)"
    echo "  --ws-url=URL    WebSocket URL (default: http://localhost:3005)"
    echo "  --timeout=MS    Request timeout in ms (default: 30000)"
    echo "  --concurrent=N  Concurrent request limit (default: 10)"
    echo ""
    echo "Examples:"
    echo "  $0 install"
    echo "  $0 check"
    echo "  $0 test"
    echo "  $0 test-small"
    echo "  $0 custom --users=75 --concurrent=15"
    echo ""
    echo "Environment Variables:"
    echo "  BASE_URL        Override default base URL"
    echo "  WEBSOCKET_URL   Override default WebSocket URL"
    echo "  USER_COUNT      Override default user count"
    echo "  TIMEOUT         Override default timeout"
    echo "  CONCURRENT_LIMIT Override default concurrent limit"
}

# Parse command line arguments
case "${1:-help}" in
    "install")
        install_deps
        ;;
    "check")
        check_services
        ;;
    "test")
        check_services && run_test 50
        ;;
    "test-small")
        check_services && run_test 10
        ;;
    "test-medium")
        check_services && run_test 25
        ;;
    "test-large")
        check_services && run_test 50
        ;;
    "test-stress")
        check_services && run_test 100
        ;;
    "custom")
        # Parse custom options
        users=$DEFAULT_USERS
        base_url=$DEFAULT_BASE_URL
        websocket_url=$DEFAULT_WEBSOCKET_URL
        timeout=$DEFAULT_TIMEOUT
        concurrent=$DEFAULT_CONCURRENT
        
        shift # Remove 'custom' from arguments
        
        while [[ $# -gt 0 ]]; do
            case $1 in
                --users=*)
                    users="${1#*=}"
                    shift
                    ;;
                --base-url=*)
                    base_url="${1#*=}"
                    shift
                    ;;
                --ws-url=*)
                    websocket_url="${1#*=}"
                    shift
                    ;;
                --timeout=*)
                    timeout="${1#*=}"
                    shift
                    ;;
                --concurrent=*)
                    concurrent="${1#*=}"
                    shift
                    ;;
                *)
                    print_warning "Unknown option: $1"
                    shift
                    ;;
            esac
        done
        
        check_services && run_test $users $base_url $websocket_url $timeout $concurrent
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
