@echo off
setlocal enabledelayedexpansion

REM ATMA E2E Load Testing Runner Script for Windows
REM This script provides easy commands to run different types of load tests

set DEFAULT_BASE_URL=http://localhost:3000
set DEFAULT_WEBSOCKET_URL=http://localhost:3005
set DEFAULT_USERS=50
set DEFAULT_TIMEOUT=30000
set DEFAULT_CONCURRENT=10

REM Function to print colored output (Windows doesn't support colors easily, so we'll use plain text)
goto :main

:print_info
echo [INFO] %~1
goto :eof

:print_success
echo [SUCCESS] %~1
goto :eof

:print_warning
echo [WARNING] %~1
goto :eof

:print_error
echo [ERROR] %~1
goto :eof

:check_services
call :print_info "Checking ATMA services..."

REM Check API Gateway
curl -s "%DEFAULT_BASE_URL%/health" >nul 2>&1
if %errorlevel% equ 0 (
    call :print_success "API Gateway is running (port 3000)"
) else (
    call :print_error "API Gateway is not running on port 3000"
    exit /b 1
)

REM Check Notification Service
curl -s "%DEFAULT_WEBSOCKET_URL%/health" >nul 2>&1
if %errorlevel% equ 0 (
    call :print_success "Notification Service is running (port 3005)"
) else (
    call :print_error "Notification Service is not running on port 3005"
    exit /b 1
)

call :print_success "All required services are running!"
goto :eof

:install_deps
call :print_info "Installing dependencies..."

if not exist "package.json" (
    call :print_error "package.json not found. Please run this script from the testing directory."
    exit /b 1
)

npm install
if %errorlevel% equ 0 (
    call :print_success "Dependencies installed successfully!"
) else (
    call :print_error "Failed to install dependencies"
    exit /b 1
)
goto :eof

:run_test
set users=%~1
set base_url=%~2
set websocket_url=%~3
set timeout=%~4
set concurrent=%~5

if "%users%"=="" set users=%DEFAULT_USERS%
if "%base_url%"=="" set base_url=%DEFAULT_BASE_URL%
if "%websocket_url%"=="" set websocket_url=%DEFAULT_WEBSOCKET_URL%
if "%timeout%"=="" set timeout=%DEFAULT_TIMEOUT%
if "%concurrent%"=="" set concurrent=%DEFAULT_CONCURRENT%

call :print_info "Starting load test with %users% users..."
call :print_info "Base URL: %base_url%"
call :print_info "WebSocket URL: %websocket_url%"
call :print_info "Timeout: %timeout%ms"
call :print_info "Concurrent Limit: %concurrent%"

node e2e-load-test.js --users=%users% --base-url=%base_url% --websocket-url=%websocket_url% --timeout=%timeout% --concurrent=%concurrent%
goto :eof

:show_help
echo ATMA E2E Load Testing Runner for Windows
echo.
echo Usage: %~nx0 [COMMAND] [OPTIONS]
echo.
echo Commands:
echo   install         Install dependencies
echo   check           Check if services are running
echo   test            Run default load test (50 users)
echo   test-small      Run small load test (10 users)
echo   test-medium     Run medium load test (25 users)
echo   test-large      Run large load test (50 users)
echo   test-stress     Run stress test (100 users)
echo   help            Show this help message
echo.
echo Examples:
echo   %~nx0 install
echo   %~nx0 check
echo   %~nx0 test
echo   %~nx0 test-small
echo.
echo Environment Variables:
echo   BASE_URL        Override default base URL
echo   WEBSOCKET_URL   Override default WebSocket URL
echo   USER_COUNT      Override default user count
echo   TIMEOUT         Override default timeout
echo   CONCURRENT_LIMIT Override default concurrent limit
goto :eof

:main
set command=%1
if "%command%"=="" set command=help

if "%command%"=="install" (
    call :install_deps
) else if "%command%"=="check" (
    call :check_services
) else if "%command%"=="test" (
    call :check_services
    if !errorlevel! equ 0 call :run_test 50
) else if "%command%"=="test-small" (
    call :check_services
    if !errorlevel! equ 0 call :run_test 10
) else if "%command%"=="test-medium" (
    call :check_services
    if !errorlevel! equ 0 call :run_test 25
) else if "%command%"=="test-large" (
    call :check_services
    if !errorlevel! equ 0 call :run_test 50
) else if "%command%"=="test-stress" (
    call :check_services
    if !errorlevel! equ 0 call :run_test 100
) else if "%command%"=="help" (
    call :show_help
) else if "%command%"=="-h" (
    call :show_help
) else if "%command%"=="--help" (
    call :show_help
) else (
    call :print_error "Unknown command: %command%"
    echo.
    call :show_help
    exit /b 1
)

endlocal
