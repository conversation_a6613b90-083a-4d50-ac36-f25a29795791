#!/bin/bash

# ATMA Services Startup Script for Testing
# This script starts all required ATMA services for load testing

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Service directories (relative to project root)
API_GATEWAY_DIR="../api-gateway"
AUTH_SERVICE_DIR="../auth-service"
ARCHIVE_SERVICE_DIR="../archive-service"
ASSESSMENT_SERVICE_DIR="../assessment-service"
NOTIFICATION_SERVICE_DIR="../notification-service"

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a service is running
check_service() {
    local url=$1
    local service_name=$2
    
    if curl -s "$url" > /dev/null 2>&1; then
        print_success "$service_name is running"
        return 0
    else
        print_warning "$service_name is not running"
        return 1
    fi
}

# Function to start a service
start_service() {
    local service_dir=$1
    local service_name=$2
    local port=$3
    
    print_info "Starting $service_name..."
    
    if [ ! -d "$service_dir" ]; then
        print_error "Service directory not found: $service_dir"
        return 1
    fi
    
    cd "$service_dir"
    
    # Check if package.json exists
    if [ ! -f "package.json" ]; then
        print_error "package.json not found in $service_dir"
        cd - > /dev/null
        return 1
    fi
    
    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        print_info "Installing dependencies for $service_name..."
        npm install
    fi
    
    # Start the service in background
    print_info "Starting $service_name on port $port..."
    npm start > "../testing/logs/${service_name}.log" 2>&1 &
    
    # Store PID for later cleanup
    echo $! > "../testing/pids/${service_name}.pid"
    
    cd - > /dev/null
    
    # Wait a moment for service to start
    sleep 3
    
    # Check if service is running
    if check_service "http://localhost:$port/health" "$service_name"; then
        print_success "$service_name started successfully on port $port"
        return 0
    else
        print_error "Failed to start $service_name"
        return 1
    fi
}

# Function to stop all services
stop_services() {
    print_info "Stopping all services..."
    
    # Kill processes using PID files
    for pid_file in pids/*.pid; do
        if [ -f "$pid_file" ]; then
            pid=$(cat "$pid_file")
            service_name=$(basename "$pid_file" .pid)
            
            if kill -0 "$pid" 2>/dev/null; then
                print_info "Stopping $service_name (PID: $pid)..."
                kill "$pid"
                rm "$pid_file"
            else
                print_warning "$service_name was not running"
                rm "$pid_file"
            fi
        fi
    done
    
    print_success "All services stopped"
}

# Function to check all services
check_all_services() {
    print_info "Checking all services..."
    
    local all_running=true
    
    check_service "http://localhost:3000/health" "API Gateway" || all_running=false
    check_service "http://localhost:3001/health" "Auth Service" || all_running=false
    check_service "http://localhost:3002/health" "Archive Service" || all_running=false
    check_service "http://localhost:3003/health" "Assessment Service" || all_running=false
    check_service "http://localhost:3005/health" "Notification Service" || all_running=false
    
    if [ "$all_running" = true ]; then
        print_success "All services are running!"
        return 0
    else
        print_warning "Some services are not running"
        return 1
    fi
}

# Function to show service status
show_status() {
    echo ""
    echo "ATMA Services Status:"
    echo "===================="
    check_service "http://localhost:3000/health" "API Gateway (3000)"
    check_service "http://localhost:3001/health" "Auth Service (3001)"
    check_service "http://localhost:3002/health" "Archive Service (3002)"
    check_service "http://localhost:3003/health" "Assessment Service (3003)"
    check_service "http://localhost:3005/health" "Notification Service (3005)"
    echo ""
}

# Function to show help
show_help() {
    echo "ATMA Services Management Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start           Start all ATMA services"
    echo "  stop            Stop all ATMA services"
    echo "  restart         Restart all ATMA services"
    echo "  status          Check status of all services"
    echo "  logs            Show logs from all services"
    echo "  help            Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 status"
    echo "  $0 stop"
}

# Create necessary directories
mkdir -p logs pids

# Main script logic
case "${1:-help}" in
    "start")
        print_info "Starting ATMA services for load testing..."
        
        # Start services in order
        start_service "$AUTH_SERVICE_DIR" "auth-service" "3001"
        start_service "$ARCHIVE_SERVICE_DIR" "archive-service" "3002"
        start_service "$ASSESSMENT_SERVICE_DIR" "assessment-service" "3003"
        start_service "$NOTIFICATION_SERVICE_DIR" "notification-service" "3005"
        start_service "$API_GATEWAY_DIR" "api-gateway" "3000"
        
        print_success "All services started!"
        show_status
        ;;
    "stop")
        stop_services
        ;;
    "restart")
        stop_services
        sleep 2
        $0 start
        ;;
    "status")
        show_status
        ;;
    "logs")
        print_info "Showing logs from all services..."
        echo ""
        for log_file in logs/*.log; do
            if [ -f "$log_file" ]; then
                service_name=$(basename "$log_file" .log)
                echo "=== $service_name logs ==="
                tail -n 20 "$log_file"
                echo ""
            fi
        done
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
