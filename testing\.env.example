# ATMA E2E Load Testing Configuration
# Copy this file to .env and modify values as needed

# API Gateway Configuration
BASE_URL=http://localhost:3000

# WebSocket Configuration  
WEBSOCKET_URL=http://localhost:3005

# Test Configuration
USER_COUNT=50
TIMEOUT=30000
CONCURRENT_LIMIT=10

# Service URLs (for reference)
AUTH_SERVICE_URL=http://localhost:3001
ARCHIVE_SERVICE_URL=http://localhost:3002
ASSESSMENT_SERVICE_URL=http://localhost:3003
NOTIFICATION_SERVICE_URL=http://localhost:3005

# Test Data Configuration
GENERATE_REALISTIC_DATA=true
USE_FAKER_SEED=false
FAKER_SEED=12345

# Debugging
DEBUG_MODE=false
VERBOSE_LOGGING=false
SAVE_TEST_RESULTS=true

# Performance Thresholds
MAX_RESPONSE_TIME_MS=5000
MIN_SUCCESS_RATE_PERCENT=95
MAX_ERROR_RATE_PERCENT=5

# Load Testing Scenarios
SMALL_LOAD_USERS=10
MEDIUM_LOAD_USERS=25
LARGE_LOAD_USERS=50
STRESS_TEST_USERS=100

# WebSocket Configuration
WEBSOCKET_TIMEOUT_MS=300000
WEBSOCKET_RECONNECT_ATTEMPTS=3
WEBSOCKET_RECONNECT_DELAY_MS=1000

# Rate Limiting
REQUESTS_PER_SECOND=100
BURST_LIMIT=200

# Test Reporting
GENERATE_HTML_REPORT=false
GENERATE_JSON_REPORT=true
REPORT_OUTPUT_DIR=./reports

# Database Configuration (if needed for cleanup)
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=atma_test
# DB_USER=test_user
# DB_PASSWORD=test_password
